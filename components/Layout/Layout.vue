<template>
	<movable-area
		:class="className"
		:attrs="$attrs"
		:style="{backgroundImage: `url(${backImg || defaultImg})`}"
	>
		<slot></slot>
	</movable-area>
</template>

<script>
	import { mapState } from 'vuex'
	
	export default {
		name:"Layout",
		props: {
			className: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				defaultImg: require('@/static/background_01.jpg')
			};
		},
		computed: {
			...mapState('app', [
				'backImg'
			])
		}
	}
</script>

<style lang="less">

</style>