<template>
	<view class="nav-bar">
		<view class="nav-left" @click="navBack">
			《  返回
		</view>
		<view class="nav-title">{{title}}</view>
	</view>
</template>

<script>
	export default {
		name:"NavBar",
		props: {
			title: {
				type: String,
				default: '页面标题'
			}
		},
		methods: {
			navBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="less">
.nav-bar{
	flex: 0;
	width: 100%;
	height: 9vh;
	line-height: 9vh;
	color: #fff;
	font-size: 4vh;
	position: relative;
	text-align: center;
	background-color: #CE2724;
	box-shadow: 0px 0.8vh 1.2vh rgba(69, 1, 0, 0.28);
	font-weight: 700;
	.nav-left{
		position: absolute;
		left: 4vh;
		top: 50%;
		transform: translateY(-50%);
		background-color: rgba(84, 0, 0, 0.65);
		height: 6vh;
		line-height: 6vh;
		text-align: center;
		font-size: 3.5vh;
		border-radius: 1.2vh;
		padding-right: 2.5vh;
		padding-left: 0.8vh;
		font-weight: normal;
	}
}
</style>