<template>
	<view
		class="agenda-item"
		:class="data.agendaStatus === 'inProgress' ? 'active' : ''"
	>
		<view class="agenda-progress" @click="toggleStatus(data)">
			<view class="progress-dot">
				<view class="dot-mini"></view>
			</view>
		</view>
		<view class="agenda-content">
			<view class="agenda-status">{{mapStatus(data.agendaStatus)}}</view>
			<view class="agenda-title" @click="handleTitleClick(data)">{{data.agendaName}}</view>
		</view>
		<view
			v-if="data.hasScore"
			class="agenda-btn agenda-code"
			@click.stop="navScore(data)"
		>
			<image class="btn-icon" src="../../static/images/icon7.png">
			<text class="btn-text">评分</text>
		</view>
		<view
			v-if="data.haveVote"
			class="agenda-btn agenda-code"
			@click.stop="navVote(data)"
		>
			<image class="btn-icon" src="../../static/images/icon3.png">
			<text class="btn-text">投票</text>
		</view>
		<!-- <view
			v-if="isHost"
			class="agenda-btn agenda-code"
			:class="{disabled: data.agendaStatus !== 'inProgress'}"
			@click.stop="showQrcode"
		>
			<image class="btn-icon" src="../../static/images/icon6.png">
			<text class="btn-text">二维码</text>
		</view> -->
		<!-- <view class="agenda-btn" :class="{disabled: data.agendaStatus !== 'inProgress'}" @click.stop="navFile(data)"> -->
		<view class="agenda-btn" @click.stop="navFile(data)">
			<image class="btn-icon" src="../../static/images/icon2.png">
			<text class="btn-text">会议资料</text>
		</view>
		<uni-popup ref="popup" type="dialog">
			<view class="dialog-body">
				<yuanqi-qr-code
					ref="yuanqiQRCode"
					:text="`${codeUrl}?id=${data.agendaId}`"
					:size="200"
					bgColor="transparent"
				></yuanqi-qr-code>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { guestUrl, apiUrl } from '@/utils/config.js'
	import { mapState } from 'vuex'

	const sealOfficeOnlineModule = uni.requireNativePlugin("Seal-OfficeOnline");

	export default {
		name:"AgendaItem",
		onLoad() {
			const { platform } = uni.getSystemInfoAsync();
			if (platform === "android") {
				this.checkWps();
			}
		},
		props: {
			data: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				codeUrl: guestUrl + '/agenda-list'
			}
		},
		computed: {
			...mapState('app', [
				'isHost',
				'userId',
				'socketTask'
			])
		},
		methods: {
			checkWps() {
				const checkWps = sealOfficeOnlineModule.checkWps();
				console.log("WPS是否安装：", checkWps);
			},
			// 展示二维码
			showQrcode() {
				this.$refs.popup.open()
			},
			// 状态映射
			mapStatus(status) {
				const mapping = {
					wait: '未开始',
					end: '已结束',
					inProgress: '进行中'
				}
				return mapping[status]
			},
			// 切换状态
			toggleStatus(data) {
				// 只有主持人能点击
				if (this.isHost) {
					const agendaId = data.agendaId
					this.$request({
						url: `/meeting/agenda/status/${agendaId}`,
						method: 'PUT'
					}).then(res => {
						this.$emit('refresh')
						this.socketTask.send({
							type: 'refreshAgenda',
							meetingId: uni.getStorageSync('meetingId'),
							from: this.userId
						})
					})
				}
			},
			// 议程标题点击事件，可直接打开主文件
			handleTitleClick(data) {
				const item = data.mainFile
				console.log('item', item);
				if (!item) return
				if (['ppt', 'pptx','pdf'].includes(item.fileType)) {
					sealOfficeOnlineModule.openFileWPS({
						url: item.url,
						openMode: 'EditMode',
						isDeleteFile: false
					}, res => {
						console.log('WPS打开文档事件结果：', res)
						if (res.result?.actionType === 'save') {
							this.uploadLocalFile(res.result.fileSavePath || res.result.filePath, apiUrl + '/meeting/annexes/saveAnnotationUrl', {
								name: 'file',
								formData: {
									meetingId: item.meetingId,
									userId: this.userId,
									name: item.fileName,
								}
							})
							.then(r => {
								console.log('fileSavePath', res.result.fileSavePath, res.result?.actionType)
								console.log('filePath', res.result.filePath, res.result?.actionType)
								console.log('上传完成:', r);
							})
							.catch(err => {
								console.error('上传出错:', err);
							});
						}
					})
				} else {
					sealOfficeOnlineModule.openFile({
						url: item.url,
						topBarTextLength: 50,
						title: item.fileName
					}, res => {
						console.log('打开在线文档事件结果：', res)
					})
				}
			},
			uploadLocalFile(sysPath, uploadUrl, options = {}) {
				return new Promise((resolve, reject) => {
					if (!sysPath) {
						return reject("文件路径不能为空");
					}
					if (!uploadUrl) {
						return reject("上传地址不能为空");
					}
					const {
						name = "file", // 上传字段名
						headers = {},
						formData = {},
					} = options;
					// 处理不同路径协议
					let pathToResolve = sysPath;
					if (sysPath.startsWith('content://')) {
						pathToResolve = sysPath; // 保持原样
					} else if (sysPath.startsWith('file://')) {
						pathToResolve = sysPath;
					} else {
						pathToResolve = 'file://' + sysPath;
					}
					// 解析原生路径并复制到 _doc 目录，避免外部存储不可读
					plus.io.resolveLocalFileSystemURL(
						pathToResolve,
						(entry) => {
							plus.io.resolveLocalFileSystemURL(
								'_doc/',
								(docDir) => {
									try {
										const extMatch = (entry.name || '').match(/\.[^\.]+$/);
										const ext = extMatch ? extMatch[0] : '';
										const newName = `upload_${Date.now()}${ext}`;
										entry.copyTo(
											docDir,
											newName,
											(copied) => {
												const usablePath = copied.toLocalURL();
												console.log('usablePath', usablePath);
												// 组合头部，自动携带 token
												const token = uni.getStorageSync('token');
												const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
												uni.uploadFile({
													url: uploadUrl,
													filePath: usablePath,
													name,
													header: finalHeaders,
													formData,
													success: (res) => {
														console.log('上传成功:', res.statusCode, res.data?.slice?.(0, 200) || res.data);
														resolve(res);
													},
													fail: (err) => {
														console.error('上传失败:', err);
														reject(err);
													}
												});
											},
											(copyErr) => {
												reject('复制到_doc失败: ' + JSON.stringify(copyErr));
											}
										);
									} catch (e) {
										reject('复制异常: ' + JSON.stringify(e));
									}
								},
								(err2) => {
									reject('访问_doc失败: ' + JSON.stringify(err2));
								}
							);
						},
						(err) => {
							reject('路径解析失败: ' + JSON.stringify(err));
						}
					);
				});
			},
			// 跳转资料页面
			navFile(data) {
				// if (data.agendaStatus === 'inProgress') {
					uni.navigateTo({
						url: `/pages/file/file?id=${data.agendaId}`
					})
				// }
			},
			// 跳转评分页面
			navScore(data) {
				uni.navigateTo({
					url: `/pages/score/score?id=${data.agendaId}`
				})
			},
			// 跳转投票页面
			navVote(data) {
				uni.navigateTo({
					url: `/pages/vote/vote?agendaId=${data.agendaId}`
				})
			}
		}
	}
</script>

<style lang="less">
.agenda-item{
	display: flex;
	justify-content: space-between;
	overflow: hidden;
	height: 23vh;
	padding: 3.5vh 6.5vh;
	&.active{
		background: linear-gradient(180deg, rgba(82, 0, 1, 0.65) 0%, rgba(82, 0, 1, 0) 116.16%);
		.agenda-progress{
			.progress-dot{
				background-color: #fbe7da;
				border-color: #ee7936;
				&::before, &::after{
					background-color: #e4312e;
				}
			}
		}
	}
	& + .agenda-item{
		.agenda-progress{
			.progress-dot::before{
				content: ''
			}
		}
	}
	.agenda-progress{
		.progress-dot{
			width: 4.5vh;
			height: 4.5vh;
			background-color: #A60028;
			border: 1.2vh solid #BF033B;
			border-radius: 50%;
			position: relative;
			z-index: 9;
			transform-style:preserve-3d;
			&::before, &::after{
				position: absolute;
				display: block;
				width: 1.3vh;
				height: 80vh;
				left: 50%;
				background-color: #9f1f2f;
				transform: translateX(-50%) translateZ(-1px);
			}
			&::before{
				content: none;
				bottom: 50%;
			}
			&::after{
				content: "";
				top: 50%;
			}
		}
	}
	.agenda-content{
		width: 100%;
		flex: 1;
		padding: 0 3.5vh;
		.agenda-status{
			display: inline-block;
			font-size: 2.5vh;
		}
		.agenda-title{
			margin-top: 1.3vh;
			font-size: 3.5vh;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
		}
	}
	.agenda-btn{
		width: 15vh;
		height: 15vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: linear-gradient(180deg, #FF6711 0%, #FFCB44 100%);
		border-bottom: 1vh solid #E58A00;
		box-shadow: 0px 1.2vh 6.5vh #98000C;
		border-radius: 2.5vh;
		align-self: center;
		& + .agenda-btn{
			margin-left: 5vh;
		}
		&.disabled{
			filter: grayscale(.8);
		}
		.btn-icon{
			width: 6vh;
			height: 6vh;
		}
		.btn-text{
			font-size: 3vh;
			font-weight: 700;
			color: #fff;
			margin-top: 0.5vh;
		}
	}
}
.dialog-body{
	background-color: #fff;
}
</style>

<style></style>