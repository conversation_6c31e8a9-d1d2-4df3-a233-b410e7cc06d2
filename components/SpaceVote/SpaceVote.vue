<template>
	<view class="space-vote">
		<view class="vote-text">投票列表</view>
		<view class="vote-content">
			<uni-row class="vote-title">
				<uni-col :span="4">序号</uni-col>
				<uni-col :span="16">投票标题</uni-col>
				<uni-col :span="4">结果</uni-col>
			</uni-row>
			<uni-row
				v-for="(item, index) in list"
				:key="item.voteId"
				class="vote-item"
			>
				<uni-col class="item-data" :span="4">{{index + 1}}</uni-col>
				<uni-col class="item-data" :span="16">{{item.topic}}</uni-col>
				<uni-col class="item-data" :span="4">
					<view @click="showDetail(item)">查看</view>
				</uni-col>
			</uni-row>
		</view>
		<uni-popup ref="popup" type="dialog">
			<view class="dialog-body">
				<uni-row class="detail-title">
					<uni-col :span="4">选项内容</uni-col>
					<uni-col :span="4">已选</uni-col>
					<uni-col :span="12">已投票人员姓名</uni-col>
					<uni-col :span="4">百分比</uni-col>
				</uni-row>
				<uni-row
					v-for="(item, index) in detailList"
					:key="index"
					class="detail-item"
				>
					<uni-col :span="4">{{item.voteContent}}</uni-col>
					<uni-col :span="4">{{item.size}}</uni-col>
					<uni-col :span="12">{{anonymousVote === '是' ? '匿名投票不能查看' : item.voteUser || '暂无人员'}}</uni-col>
					<uni-col :span="4">{{item.percent}}</uni-col>
				</uni-row>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name: "SpaceVote",
		props: {
			list: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				anonymousVote: false,
				detailList: []
			}
		},
		methods: {
			showDetail(item) {
				this.anonymousVote = item.anonymousVote
				this.$request({
					url: `/meeting/vote/current/detail/${item.voteId}`
				}).then(res => {
					this.detailList = res.data || []
					this.$refs.popup.open()
				})
			}
		}
	}
</script>

<style lang="less">
.space-vote{
	padding: 2vh 3vh;
	.vote-text{
		font-size: 3vh;
	}
	.vote-content{
		padding: 2vh;
		text-align: center;
		.vote-title{
			background-color: #540000A6;
			height: 6vh;
			line-height: 6vh;
			font-size: 3vh;
			color: #fff;
		}
		.vote-item{
			background-color: #fff;
			border-bottom: 2rpx solid #ed8c97;
			height: 14vh;
			vertical-align: middle;
			font-size: 3vh;
			.item-data{
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
	.dialog-body{
		width: 60vw;
		background-color: #fff;
		border-radius: 2vh;
		padding: 5vh;
		max-height: 70vh;
		overflow-y: auto;
		text-align: center;
		.detail-title{
			background-color: #540000A6;
			height: 6vh;
			line-height: 6vh;
			font-size: 3vh;
			color: #fff;
		}
		.detail-item{
			background-color: #fff;
			border-bottom: 2rpx solid #ed8c97;
			height: 6vh;
			line-height: 6vh;
			vertical-align: middle;
			font-size: 3vh;
			.item-data{
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}
</style>