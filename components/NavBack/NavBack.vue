<template>
	<view class="nav-back" @click="navBack">
		《  返回
	</view>
</template>

<script>
	export default {
		name: "NavBack",
		data() {
			return {
				
			};
		},
		methods: {
			navBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="less">
.nav-back{
	position: fixed;
	top: 20rpx;
	left: 20rpx;
	width: 96rpx;
	height: 36rpx;
	padding: 4rpx 6rpx 4rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 8rpx;
	background-color: rgba(84, 0, 0, 0.65);
	font-size: 20rpx;
	cursor: pointer;
	color: #fff;
}
</style>