<template>
  <view class="file-item" @click="downloadFile(file)">
    <image class="file-icon" :src="mapIcon(file.fileType)"></image>
    <view class="file-name">{{ file.fileName }}</view>
  </view>
</template>

<script>
import { mapState } from "vuex";
import { apiUrl } from "@/utils/config.js";

const sealOfficeOnlineModule = uni.requireNativePlugin("Seal-OfficeOnline");

export default {
  name: "FileItem",
  onLoad() {
    const { platform } = uni.getSystemInfoAsync();
    if (platform === "android") {
      this.checkWps();
    }
  },
  props: {
    file: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapState("app", ["userId", "cacheFiles"]),
  },
  methods: {
    checkWps() {
      const checkWps = sealOfficeOnlineModule.checkWps();
      console.log("WPS是否安装：", checkWps);
    },
    // 图标渲染
    mapIcon(type) {
      const icon_doc = require("@/static/images/icon_doc.png");
      const icon_ppt = require("@/static/images/icon_ppt.png");
      const icon_pdf = require("@/static/images/icon_pdf.png");
      const icon_excel = require("@/static/images/icon_excel.png");
      const mapping = {
        xls: icon_excel,
        xlsx: icon_excel,
        doc: icon_doc,
        docx: icon_doc,
        ppt: icon_ppt,
        pptx: icon_ppt,
        pdf: icon_pdf,
      };
      return mapping[type];
    },
    // 下载文件
    downloadFile(item) {
      console.log('item', item);
      if (['ppt', 'pptx','pdf'].includes(item.fileType)) {
      	sealOfficeOnlineModule.openFileWPS({
      		url: item.url,
      		openMode: 'EditMode',
      		isDeleteFile: false
      	}, res => {
      		console.log('WPS打开文档事件结果：', res)
      		if(res.result?.actionType === 'save'){
      			this.uploadLocalFile(res.result.fileSavePath || res.result.filePath, apiUrl + '/meeting/annexes/saveAnnotationUrl', {
      				name: 'file',
      				formData: {
      					meetingId: item.meetingId,
                userId: this.userId,
                name: item.fileName,
      				}
      			})
      			.then(res => {
      				console.log('上传完成:', res);
      			})
      			.catch(err => {
      				console.error('上传出错:', err);
      			});
      		}
      	})
      } else {
      	sealOfficeOnlineModule.openFile({
      		url: item.url,
      		topBarTextLength: 50,
      		title: item.fileName
      	}, res => {
      		console.log('打开在线文档事件结果：', res)
      	})
      }
    },
    // 改用公共上传方法
    uploadLocalFile(sysPath, uploadUrl, options = {}) {
      const { uploadLocalFile } = require('@/utils/common.js')
      return uploadLocalFile(sysPath, uploadUrl, options)
    }
  },
};
</script>

<style lang="less">
.file-item {
  color: #fff;
  font-size: 3.5vh;
  font-weight: 600;
  padding: 3vh 5vh;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #cecece;
  .file-icon {
    width: 5vh;
    height: 5vh;
    margin-right: 3vh;
  }
}
</style>