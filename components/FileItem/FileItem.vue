<template>
  <view class="file-item" @click="downloadFile(file)">
    <image class="file-icon" :src="mapIcon(file.fileType)"></image>
    <view class="file-name">{{ file.fileName }}</view>
  </view>
</template>

<script>
import { mapState } from "vuex";
import { apiUrl } from "@/utils/config.js";

const sealOfficeOnlineModule = uni.requireNativePlugin("Seal-OfficeOnline");
const wpsTool = uni.requireNativePlugin("CL-WPSTool");
export default {
  name: "FileItem",
  onLoad() {
    const { platform } = uni.getSystemInfoAsync();
    if (platform === "android") {
      this.checkWps();
    }
  },
  props: {
    file: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapState("app", ["userId", "cacheFiles"]),
  },
  methods: {
    checkWps() {
      const checkWps = sealOfficeOnlineModule.checkWps();
      console.log("WPS是否安装：", checkWps);
    },
    // 图标渲染
    mapIcon(type) {
      const icon_doc = require("@/static/images/icon_doc.png");
      const icon_ppt = require("@/static/images/icon_ppt.png");
      const icon_pdf = require("@/static/images/icon_pdf.png");
      const icon_excel = require("@/static/images/icon_excel.png");
      const mapping = {
        xls: icon_excel,
        xlsx: icon_excel,
        doc: icon_doc,
        docx: icon_doc,
        ppt: icon_ppt,
        pptx: icon_ppt,
        pdf: icon_pdf,
      };
      return mapping[type];
    },
    // 下载文件
    downloadFile(item) {
      console.log('item', item);
      if (['ppt', 'pptx','pdf'].includes(item.fileType)) {
      	sealOfficeOnlineModule.openFileWPS({
      		url: item.url,
      		openMode: 'EditMode',
      		isDeleteFile: false
      	}, res => {
      		console.log('WPS打开文档事件结果：', res)
      		if(res.result?.actionType === 'save'){
      			this.uploadLocalFile(res.result.fileSavePath || res.result.filePath, apiUrl + '/meeting/annexes/saveAnnotationUrl', {
      				name: 'file',
      				formData: {
      					meetingId: item.meetingId,
                userId: this.userId,
                name: item.fileName,
      				}
      			})
      			.then(res => {
      				console.log('上传完成:', res);
      			})
      			.catch(err => {
      				console.error('上传出错:', err);
      			});
      		}
      	})
      } else {
      	sealOfficeOnlineModule.openFile({
      		url: item.url,
      		topBarTextLength: 50,
      		title: item.fileName
      	}, res => {
      		console.log('打开在线文档事件结果：', res)
      	})
      }
    },
    uploadLocalFile(sysPath, uploadUrl, options = {}) {
      return new Promise((resolve, reject) => {
        if (!sysPath) {
          return reject("文件路径不能为空");
        }
        if (!uploadUrl) {
          return reject("上传地址不能为空");
        }
        const {
          name = "file", // 上传字段名
          headers = {},
          formData = {},
        } = options;
        // 处理不同路径协议
        let pathToResolve = sysPath;
        if (sysPath.startsWith('content://')) {
          pathToResolve = sysPath; // 保持原样
        } else if (sysPath.startsWith('file://')) {
          pathToResolve = sysPath;
        } else {
          pathToResolve = 'file://' + sysPath;
        }
        // 解析原生路径并复制到 _doc 目录，避免外部存储不可读
        plus.io.resolveLocalFileSystemURL(
          pathToResolve,
          (entry) => {
            plus.io.resolveLocalFileSystemURL(
              '_doc/',
              (docDir) => {
                try {
                  const extMatch = (entry.name || '').match(/\.[^\.]+$/);
                  const ext = extMatch ? extMatch[0] : '';
                  const newName = `upload_${Date.now()}${ext}`;
                  entry.copyTo(
                    docDir,
                    newName,
                    (copied) => {
                      const usablePath = copied.toLocalURL();
                      console.log('usablePath', usablePath);
                      const token = uni.getStorageSync('token');
                      const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
                      uni.uploadFile({
                        url: uploadUrl,
                        filePath: usablePath,
                        name,
                        header: finalHeaders,
                        formData,
                        success: (res) => {
                          console.log('上传成功:', res.statusCode, res.data?.slice?.(0, 200) || res.data);
                          resolve(res);
                        },
                        fail: (err) => {
                          console.error('上传失败:', err);
                          reject(err);
                        },
                      });
                    },
                    (copyErr) => {
                      // Android 10+ 无法拷贝时，直接以原路径上传（包括 content://）
                      console.warn('copyTo失败，尝试直接上传原路径', copyErr);
                      const token = uni.getStorageSync('token');
                      const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
                      uni.uploadFile({
                        url: uploadUrl,
                        filePath: pathToResolve,
                        name,
                        header: finalHeaders,
                        formData,
                        success: (res) => {
                          console.log('上传成功(fallback):', res.statusCode, res.data?.slice?.(0, 200) || res.data);
                          resolve(res);
                        },
                        fail: (err) => {
                          console.error('上传失败(fallback):', err);
                          reject('复制到_doc失败: ' + JSON.stringify(copyErr));
                        },
                      });
                    }
                  );
                } catch (e) {
                  reject('复制异常: ' + JSON.stringify(e));
                }
              },
              (err2) => {
                reject('访问_doc失败: ' + JSON.stringify(err2));
              }
            );
          },
          (err) => {
            reject('路径解析失败: ' + JSON.stringify(err));
          }
        );
      });
    },
  },
};
</script>

<style lang="less">
.file-item {
  color: #fff;
  font-size: 3.5vh;
  font-weight: 600;
  padding: 3vh 5vh;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #cecece;
  .file-icon {
    width: 5vh;
    height: 5vh;
    margin-right: 3vh;
  }
}
</style>