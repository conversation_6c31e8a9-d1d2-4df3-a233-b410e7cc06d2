<template>
	<view class="space-sign">
		<view class="sign-text">签到列表</view>
		<view class="sign-content">
			<uni-row class="sign-title">
				<uni-col :span="4">姓名</uni-col>
				<uni-col :span="6">电话</uni-col>
				<uni-col :span="4">状态</uni-col>
				<uni-col :span="10">签到时间</uni-col>
			</uni-row>
			<uni-row
				v-for="(item, index) in list"
				:key="item.signId"
				class="sign-item"
			>
				<uni-col class="item-data" :span="4">{{item.userName}}</uni-col>
				<uni-col class="item-data" :span="6">{{item.phone}}</uni-col>
				<uni-col class="item-data" :span="4">{{item.signType}}</uni-col>
				<uni-col class="item-data" :span="10">{{item.signTime}}</uni-col>
			</uni-row>
		</view>
	</view>
</template>

<script>
	export default {
		name: "SpaceSign",
		props: {
			list: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="less">
.space-sign{
	padding: 2vh 3vh;
	.sign-text{
		font-size: 3vh;
	}
	.sign-content{
		padding: 2vh;
		text-align: center;
		.sign-title{
			background-color: #540000A6;
			height: 6vh;
			line-height: 6vh;
			font-size: 3vh;
			color: #fff;
		}
		.sign-item{
			background-color: #fff;
			border-bottom: 2rpx solid #ed8c97;
			height: 8vh;
			vertical-align: middle;
			font-size: 3vh;
			.item-data{
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}
</style>