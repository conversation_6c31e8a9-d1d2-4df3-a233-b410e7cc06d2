<template>
	<view class="meeting-ctrl">
		<view v-if="showBack" class="ctrl-btn nav-back" @click="handleBack">返回</view>
		<view class="ctrl-btn log-out" @click="handleQuit">
			退出
		</view>
	</view>
</template>

<script>
	export default {
		name: "LogOut",
		props: {
			showBack: {
				type: Boolean,
				default: true
			}
		},
		methods: {
			// 返回上一页
			handleBack() {
				uni.removeStorageSync('roomId')
				uni.redirectTo({
					url: '/pages/index/index'
				})
			},
			// 退出应用
			handleQuit() {
				uni.showModal({
					title: '提示',
					content: '确定要退出应用吗？',
					success(res) {
						if (res.confirm) {
							plus.runtime.quit()
						}
					}
				})
			}
		}
	}
</script>

<style lang="less">
.meeting-ctrl{
	position: fixed;
	top: 12rpx;
	right: 24rpx;
	display: flex;
	.ctrl-btn{
		height: 36rpx;
		line-height: 36rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 8rpx;
		background-color: rgba(84, 0, 0, 0.65);
		font-size: 20rpx;
		cursor: pointer;
		color: #fff;
		padding-right: 15rpx;
		padding-left: 15px;
		z-index: 99;
		& + .ctrl-btn{
			margin-left: 24px;
		}
	}
}
</style>