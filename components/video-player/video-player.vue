<template>
	<video 
		v-if="isSharing && !meSharing"
		class="screen-video"
		id="myVideo"
		objectFit="cover"
		is-live
		:controls="false"
		autoplay
		:src="videoSrc"
	></video>
</template>

<script>
	import { mapState } from 'vuex'
	
	export default {
		name:"video-player",
		data() {
			return {
			};
		},
		computed: {
			...mapState('app', [
				'isSharing',
				'videoSrc',
				'meSharing'
			])
		},
		watch: {
			isSharing(val) {
				console.log('isSharing', val)
				if (val) {
					this.$nextTick(() => {
						const videoContext = uni.createVideoContext('myVideo', this)
						videoContext.play()
					})
				}
			}
		}
	}
</script>

<style lang="scss">
.screen-video{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100vw;
	height: 100vh;
	z-index: 999;
}
</style>