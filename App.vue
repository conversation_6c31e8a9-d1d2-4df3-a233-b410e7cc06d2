<script>
	import { mapState } from 'vuex'
	import { socketUrl } from '@/utils/config.js'
	import wsRequest from './utils/socket.js'
	
	export default {
		computed: {
			...mapState('app', [
				'userId',
				'socketTask'
			])
		},
		onLaunch: function() {
			// #ifdef APP
			plus.navigator.setFullscreen(true)
			this.checkUpdate()
			// #endif
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
			// 从后台切回前台需要判断重新连接socket
			if (this.userId) {
				if (!this.socketTask) {
					const socketTask = new wsRequest(`${socketUrl}/webSocket/`, this.userId)
					this.$store.commit('app/SET_SOCKET_TASK', socketTask)
				}
			}
		},
		onHide: function() {
			console.log('App Hide')
		},
		onBackPress(event) {
			console.log('event', event)
			return true
		},
		methods: {
			// 检查更新
			checkUpdate() {
				const params = {
					version: plus.runtime.version
				}
				this.$request({
					url: `/system/version/ota`,
					needLogin: false,
					data: params
				}).then(res => {
					if (res.code === 200 && res.data) {
						uni.showModal({ //提醒用户更新
							title: "更新提示",
							content: res.data.note,
							success: (r) => {
								if (r.confirm) {
									plus.runtime.openURL(res.data.url)
								}
							}
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import '@/uni_modules/uni-scss/index.scss';
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';
	/* #endif */
	@font-face {
		font-family: 'hyxk';
		src: url('~@/static/HanYiXingKaiJian-1.ttf') format('truetype');
	}
</style>
