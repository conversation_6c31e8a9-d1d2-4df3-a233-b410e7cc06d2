const state = {
	backImg: undefined,
	fontColor: undefined,
	userId: undefined,
	userName: '',
	isScreen: false, // 是否有同屏权限
	isSharing: false,
	meSharing: false,
	videoSrc: '',
	// 是否主持人
	isHost: false,
	isWaiter: false,
	socketTask: null,
	cacheFiles: {},
	serviceTag: false
}

const mutations = {
	SET_BACK_IMG: (state, payload) => {
		state.backImg = payload
	},
	SET_FONT_COLOR: (state, payload) => {
		state.fontColor = payload
	},
	TOGGLE_SCREEN(state, payload) {
		state.isScreen = payload
	},
	TOGGLE_SHARING: (state, payload) => {
		state.isSharing = payload
	},
	TOGGLE_ME_SHARING: (state, payload) => {
		state.meSharing = payload
	},
	SET_VIDEO_SRC: (state, payload) => {
		state.videoSrc = payload
	},
	SET_USER_ID: (state, payload) => {
		state.userId = payload
		console.log('设置userId成功： ' + payload)
	},
	SET_USERNAME: (state, payload) => {
		state.userName = payload
	},
	SET_HOST: (state, payload) => {
		state.isHost = payload
	},
	SET_WAITER: (state, payload) => {
		state.isWaiter = payload
	},
	SET_SOCKET_TASK: (state, payload) => {
		state.socketTask = payload
	},
	SET_CACHE_FILES: (state, payload) => {
		state.cacheFiles = Object.assign(state.cacheFiles, {
			[payload.key]: payload.value
		})
	},
	SET_SERVICE_TAG: (state, payload) => {
		state.serviceTag = payload
	}
}

export default {
	namespaced: true,
	state,
	mutations
}