export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 处理外部存储文件（Android 10+ 作用域存储兼容）
function handleExternalStorageFile(sysPath, uploadUrl, options, resolve, reject) {
  const { name = 'file', headers = {}, formData = {} } = options;

  // 提取文件名
  const fileName = sysPath.split('/').pop();
  const extMatch = fileName.match(/\.[^\.]+$/);
  const ext = extMatch ? extMatch[0] : '';

  // 尝试通过 plus.io.getFileInfo 读取文件
  plus.io.getFileInfo({
    filePath: sysPath,
    success: (fileInfo) => {
      console.log('外部存储文件信息:', fileInfo);
      // 创建临时文件到 _doc
      const tempName = `temp_${Date.now()}${ext}`;
      plus.io.resolveLocalFileSystemURL('_doc/', (docDir) => {
        // 尝试读取原文件内容并写入临时文件
        plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS, 0, (fs) => {
          fs.root.getFile(fileName, { create: false }, (fileEntry) => {
            fileEntry.file((file) => {
              const reader = new plus.io.FileReader();
              reader.onloadend = () => {
                // 写入到 _doc 临时文件
                docDir.getFile(tempName, { create: true }, (tempEntry) => {
                  tempEntry.createWriter((writer) => {
                    writer.write(reader.result);
                    writer.onwriteend = () => {
                      // 上传临时文件
                      const token = uni.getStorageSync('token');
                      const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
                      uni.uploadFile({
                        url: uploadUrl,
                        filePath: tempEntry.toLocalURL(),
                        name,
                        header: finalHeaders,
                        formData,
                        success: (res) => {
                          console.log('上传成功(外部存储):', res.statusCode, res.data?.slice?.(0, 200) || res.data);
                          resolve(res);
                        },
                        fail: (err) => {
                          console.error('上传失败(外部存储):', err);
                          reject(err);
                        }
                      });
                    };
                  });
                });
              };
              reader.readAsArrayBuffer(file);
            });
          }, () => {
            // 文件访问失败，回退到直接上传
            console.warn('无法访问外部存储文件，尝试直接上传');
            fallbackDirectUpload(sysPath, uploadUrl, options, resolve, reject);
          });
        }, () => {
          fallbackDirectUpload(sysPath, uploadUrl, options, resolve, reject);
        });
      });
    },
    fail: () => {
      console.warn('获取外部存储文件信息失败，尝试直接上传');
      fallbackDirectUpload(sysPath, uploadUrl, options, resolve, reject);
    }
  });
}

// 回退：直接上传（可能失败但作为最后手段）
function fallbackDirectUpload(sysPath, uploadUrl, options, resolve, reject) {
  const { name = 'file', headers = {}, formData = {} } = options;
  const token = uni.getStorageSync('token');
  const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };

  uni.uploadFile({
    url: uploadUrl,
    filePath: sysPath,
    name,
    header: finalHeaders,
    formData,
    success: (res) => {
      console.log('上传成功(直接):', res.statusCode, res.data?.slice?.(0, 200) || res.data);
      resolve(res);
    },
    fail: (err) => {
      console.error('上传失败(直接):', err);
      reject('外部存储文件无法访问: ' + sysPath);
    }
  });
}
// 通用：本地文件上传（支持 content:// 与 file://，Android10+ 作用域存储兼容）
export function uploadLocalFile(sysPath, uploadUrl, options = {}) {
  return new Promise((resolve, reject) => {
    if (!sysPath) {
      return reject('文件路径不能为空');
    }
    if (!uploadUrl) {
      return reject('上传地址不能为空');
    }
    const { name = 'file', headers = {}, formData = {} } = options;

    // 处理路径协议
    let pathToResolve = sysPath;
    if (sysPath.startsWith('content://')) {
      // 直接上传 content://，避免 Android10+ 拷贝限制
      const token = uni.getStorageSync('token');
      const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
      return uni.uploadFile({
        url: uploadUrl,
        filePath: pathToResolve,
        name,
        header: finalHeaders,
        formData,
        success: (res) => {
          console.log('上传成功(content):', res.statusCode, res.data?.slice?.(0, 200) || res.data);
          resolve(res);
        },
        fail: (err) => {
          console.error('上传失败(content):', err);
          reject(err);
        }
      });
    } else if (!sysPath.startsWith('file://')) {
      pathToResolve = 'file://' + sysPath;
    }

    // 检查是否为外部存储路径（Android 10+ 受限）
    if (sysPath.includes('/storage/emulated/') || sysPath.includes('/sdcard/')) {
      console.log('检测到外部存储路径，尝试通过 MediaStore 访问:', sysPath);
      // 对于外部存储文件，尝试通过文件名在 MediaStore 中查找并复制
      return handleExternalStorageFile(sysPath, uploadUrl, options, resolve, reject);
    }

    // file:// 路径：解析并优先复制到 _doc/，失败则回退直传
    plus.io.resolveLocalFileSystemURL(
      pathToResolve,
      (entry) => {
        plus.io.resolveLocalFileSystemURL(
          '_doc/',
          (docDir) => {
            try {
              const extMatch = (entry.name || '').match(/\.[^\.]+$/);
              const ext = extMatch ? extMatch[0] : '';
              const newName = `upload_${Date.now()}${ext}`;
              entry.copyTo(
                docDir,
                newName,
                (copied) => {
                  const usablePath = copied.toLocalURL();
                  console.log('usablePath', usablePath);
                  const token = uni.getStorageSync('token');
                  const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
                  uni.uploadFile({
                    url: uploadUrl,
                    filePath: usablePath,
                    name,
                    header: finalHeaders,
                    formData,
                    success: (res) => {
                      console.log('上传成功:', res.statusCode, res.data?.slice?.(0, 200) || res.data);
                      resolve(res);
                    },
                    fail: (err) => {
                      console.error('上传失败:', err);
                      reject(err);
                    },
                  });
                },
                (copyErr) => {
                  // 拷贝失败：直接上传原始路径（包括 file://）
                  console.warn('copyTo失败，尝试直接上传原路径', copyErr);
                  const token = uni.getStorageSync('token');
                  const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
                  uni.uploadFile({
                    url: uploadUrl,
                    filePath: pathToResolve,
                    name,
                    header: finalHeaders,
                    formData,
                    success: (res) => {
                      console.log('上传成功(fallback):', res.statusCode, res.data?.slice?.(0, 200) || res.data);
                      resolve(res);
                    },
                    fail: (err) => {
                      console.error('上传失败(fallback):', err);
                      reject('复制到_doc失败: ' + JSON.stringify(copyErr));
                    },
                  });
                }
              );
            } catch (e) {
              reject('复制异常: ' + JSON.stringify(e));
            }
          },
          (err2) => {
            reject('访问_doc失败: ' + JSON.stringify(err2));
          }
        );
      },
      (err) => {
        reject('路径解析失败: ' + JSON.stringify(err));
      }
    );
  });
}
