export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 通用：本地文件上传（支持 content:// 与 file://，Android10+ 作用域存储兼容）
export function uploadLocalFile(sysPath, uploadUrl, options = {}) {
  return new Promise((resolve, reject) => {
    if (!sysPath) {
      return reject('文件路径不能为空');
    }
    if (!uploadUrl) {
      return reject('上传地址不能为空');
    }
    const { name = 'file', headers = {}, formData = {} } = options;

    // 处理路径协议
    let pathToResolve = sysPath;
    if (sysPath.startsWith('content://')) {
      // 直接上传 content://，避免 Android10+ 拷贝限制
      const token = uni.getStorageSync('token');
      const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
      return uni.uploadFile({
        url: uploadUrl,
        filePath: pathToResolve,
        name,
        header: finalHeaders,
        formData,
        success: (res) => {
          console.log('上传成功(content):', res.statusCode, res.data?.slice?.(0, 200) || res.data);
          resolve(res);
        },
        fail: (err) => {
          console.error('上传失败(content):', err);
          reject(err);
        }
      });
    } else if (!sysPath.startsWith('file://')) {
      pathToResolve = 'file://' + sysPath;
    }

    // file:// 路径：解析并优先复制到 _doc/，失败则回退直传
    plus.io.resolveLocalFileSystemURL(
      pathToResolve,
      (entry) => {
        plus.io.resolveLocalFileSystemURL(
          '_doc/',
          (docDir) => {
            try {
              const extMatch = (entry.name || '').match(/\.[^\.]+$/);
              const ext = extMatch ? extMatch[0] : '';
              const newName = `upload_${Date.now()}${ext}`;
              entry.copyTo(
                docDir,
                newName,
                (copied) => {
                  const usablePath = copied.toLocalURL();
                  console.log('usablePath', usablePath);
                  const token = uni.getStorageSync('token');
                  const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
                  uni.uploadFile({
                    url: uploadUrl,
                    filePath: usablePath,
                    name,
                    header: finalHeaders,
                    formData,
                    success: (res) => {
                      console.log('上传成功:', res.statusCode, res.data?.slice?.(0, 200) || res.data);
                      resolve(res);
                    },
                    fail: (err) => {
                      console.error('上传失败:', err);
                      reject(err);
                    },
                  });
                },
                (copyErr) => {
                  // 拷贝失败：直接上传原始路径（包括 file://）
                  console.warn('copyTo失败，尝试直接上传原路径', copyErr);
                  const token = uni.getStorageSync('token');
                  const finalHeaders = token ? { Authorization: 'Bearer ' + token, ...headers } : { ...headers };
                  uni.uploadFile({
                    url: uploadUrl,
                    filePath: pathToResolve,
                    name,
                    header: finalHeaders,
                    formData,
                    success: (res) => {
                      console.log('上传成功(fallback):', res.statusCode, res.data?.slice?.(0, 200) || res.data);
                      resolve(res);
                    },
                    fail: (err) => {
                      console.error('上传失败(fallback):', err);
                      reject('复制到_doc失败: ' + JSON.stringify(copyErr));
                    },
                  });
                }
              );
            } catch (e) {
              reject('复制异常: ' + JSON.stringify(e));
            }
          },
          (err2) => {
            reject('访问_doc失败: ' + JSON.stringify(err2));
          }
        );
      },
      (err) => {
        reject('路径解析失败: ' + JSON.stringify(err));
      }
    );
  });
}
