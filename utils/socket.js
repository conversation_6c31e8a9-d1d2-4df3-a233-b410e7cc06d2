import bus from './eventBus.js'
import store from '@/store/index.js'

class wsRequest {
	constructor(url, userId, time = 10000) {
		this.status = null
		this.lockReconnect = false
		this.url = url
		this.userId = userId
		this.socketTask = null
		
		// 心跳检测
		this.timeout = time
		this.timeoutObj = null
		this.reconnectTimeoutObj = null
		
		try {
			return this.initRequest()
		} catch (e) {
			console.log('catch')
			this.reconnect()
		}
	}
	initRequest() {
		this.socketTask = uni.connectSocket({
			url: this.url + this.userId,
			success: () => {
				console.log('连接成功')
				return this.socketTask
			}
		})
		this.socketTask.onOpen(res => {
			uni.showToast({
				position: 'bottom',
				title: '连接成功',
				icon: 'none'
			})
			clearTimeout(this.reconnectTimeoutObj)
			this.start()
		})
		this.socketTask.onClose(res => {
			console.log(res, '连接关闭')
			this.socketTask = null
			this.reconnect()
		})
		this.socketTask.onError(err => {
			console.log(err, '连接错误')
			this.socketTask = null
			this.reconnect()
		})
		this.socketTask.onMessage(res => {
			this.reset()
			console.log('收到服务器内容app.vue', res.data)
			const resObj = JSON.parse(res.data)
			// 刷新议程
			if (resObj.type === 'refreshAgenda') {
				bus.$emit('refresh-agenda')
			}
			// 会议服务
			if (resObj.type === 'service') {
				store.commit('app/SET_SERVICE_TAG', true)
			}
			// 开始同屏
			if (resObj.type === 'screenStart') {
				console.log('screenStart', resObj.url)
				store.commit('app/SET_VIDEO_SRC', resObj.url)
				store.commit('app/TOGGLE_SHARING', true)
				if (!store.state.app.meSharing) {
					uni.navigateTo({
						url: '/pages/video/video'
					})
				}
			}
			// 关闭同屏
			if (resObj.type === 'screenEnd') {
				store.commit('app/SET_VIDEO_SRC', '')
				store.commit('app/TOGGLE_SHARING', false)
			}
		})
	}
	send(value) {
		return new Promise((resolve, reject) => {
			console.log('send')
			this.socketTask.send({
				data: JSON.stringify(value),
				success: () => {
					console.log('socket 发送成功', value)
					resolve('发送成功')
				}
			})
		})
	}
	// reset和start方法主要用来控制心跳的定时
	reset() {
		clearTimeout(this.timeoutObj)
		this.start()
	}
	start() {
		this.timeoutObj = setTimeout(() => {
			console.log('ping')
			this.socketTask.send({data: 'ping'})
			this.start()
		}, this.timeout)
	}
	reconnect() {
		// 防止多个方法调用，多处重连
		if (this.lockReconnect) {
			return
		}
		this.lockReconnect = true
		console.log('准备重连')
		this.reconnectTimeoutObj = setTimeout(() => {
			this.initRequest()
			this.lockReconnect = false
		}, 3000)
	}
	// 手动关闭
	close() {
		this.socketTask.close()
	}
}

export default wsRequest