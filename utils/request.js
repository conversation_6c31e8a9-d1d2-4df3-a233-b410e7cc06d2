import { apiUrl } from './config.js'

const request = (opt = {needLogin: true}) => {
	const options = {
		url: opt.url || '',
		method: opt.method || 'GET',
		data: opt.data || {},
		needLogin: opt.needLogin || true
	}
	return new Promise((resove, reject) => {
		const config = {
			url: apiUrl + options.url,
			method: options.method,
			data: options.data
		}
		if (options.needLogin) {
			const token = uni.getStorageSync('token')
			config.header = { 'Authorization': 'Bearer ' + token }
		}
		uni.request({
			...config,
			success(res) {
				const { data } = res
				if (data.code === 200) {
					resove(data)
				} else {
					uni.showToast({
						title: data.msg || data.error,
						icon: 'none'
					})
					reject(data)
				}
			},
			fail(err) {
				reject(err)
			}
		})
	})
}

export default request