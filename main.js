
// #ifndef VUE3
import Vue from 'vue'
import App from './App'
import store from 'store'
import bus from './utils/eventBus.js'
import request from './utils/request.js'
import Layout from './components/Layout/Layout.vue'
import NavBar from './components/NavBar/NavBar.vue'
import LogOut from './components/LogOut/LogOut.vue'
import mixin from '@/utils/mixin.js'

Vue.config.productionTip = false
Vue.prototype.toJSON = () => {}
Vue.prototype.$bus = bus
Vue.prototype.$request = request
Vue.mixin(mixin)

Vue.component('Layout', Layout)
Vue.component('NavBar', NavBar)
Vue.component('LogOut', LogOut)

App.mpType = 'app'

const app = new Vue({
    ...App,
		store
})
app.$mount()

// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import App from './App.vue'
import modalTestVue from './components/modal-test/modal-test.vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif