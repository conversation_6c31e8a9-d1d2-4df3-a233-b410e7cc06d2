{
    "name" : "智能会议",
    "appid" : "__UNI__D8B1FD7",
    "description" : "",
    "versionName" : "1.1.9",
    "versionCode" : 101,
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "VideoPlayer" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"
                ]
            },
            "ios" : {
                "dSYMs" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "static/images/72.png",
                    "xhdpi" : "static/images/96.png",
                    "xxhdpi" : "static/images/144.png",
                    "xxxhdpi" : "static/images/192.png"
                }
            }
        },
        "nativePlugins" : {
            "yuange-YGRSPushModule" : {
                "__plugin_info__" : {
                    "name" : "手机边录制屏幕边推流、录屏直播",
                    "description" : "支持手机边录制屏幕边推流、录屏直播",
                    "platforms" : "Android",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=9217",
                    "android_package_name" : "uni.UNID8B1FD7",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "9217",
                    "parameters" : {}
                }
            },
            "Seal-OfficeOnline" : {
                "__plugin_info__" : {
                    "name" : "跨平台Office文档预览原生插件【非X5离线、组件嵌入、水印、WPS预览编辑】",
                    "description" : "非腾讯X5，无内核加载，稳定高可用，在线文档URL和离线均可使用，支持Android和IOS，支持Office文档，常用图片和音视频格式",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=3226",
                    "android_package_name" : "uni.UNID8B1FD7",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "3226",
                    "parameters" : {}
                }
            },
            "Ba-KeepAliveSuit" : {
                "__plugin_info__" : {
                    "name" : "安卓保活套装（通用、常驻通知、电池优化、自启管理、后台运行等）",
                    "description" : "在Ba-KeepAlive的基础上，新增了忽略电池优化、自启管理、常驻通知。为定位、推送、websocket、定时任务、蓝牙、聊天等保驾护航（**注意：**不保证支持所有机型和场景，建议先试用再购买）",
                    "platforms" : "Android",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=11765",
                    "android_package_name" : "uni.UNID8B1FD7",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "11765",
                    "parameters" : {}
                }
            },
            "CL-WPSTool" : {
                "__plugin_info__" : {
                    "name" : "集成wps 文档编辑和预览功能",
                    "description" : "集成第三方wps，文档编辑(支持编辑留痕)、文档预览功能",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=2459",
                    "android_package_name" : "uni.UNID8B1FD7",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "2459",
                    "parameters" : {}
                }
            }
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "vueVersion" : "2",
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : true
        }
    }
}
