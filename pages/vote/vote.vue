<template>
	<layout className="app-container vote">
		<nav-bar title="投票表决"></nav-bar>
		<view class="vote-content">
			<uni-row class="vote-title">
				<uni-col :span="4">状态</uni-col>
				<uni-col :span="16">投票标题</uni-col>
				<uni-col :span="4">操作</uni-col>
			</uni-row>
			<uni-row
				v-for="item in voteList"
				:key="item.voteId"
				class="vote-item"
			>
				<uni-col class="item-data" :span="4">{{mapStatus(item.voteStatus)}}</uni-col>
				<uni-col class="item-data" :span="16">{{item.topic}}</uni-col>
				<uni-col class="item-data" :span="4">
					<image class="vote-icon" src="@/static/images/hammer.png" @click="navDetail(item)">
				</uni-col>
			</uni-row>
		</view>
	</layout>
</template>

<script>
	export default {
		data() {
			return {
				agendId: undefined,
				voteList: []
			}
		},
		onShow() {
			this.queryVoteList()
		},
		methods: {
			// 获取投票列表
			queryVoteList() {
				const curPage = getCurrentPages()
				const agendaId = curPage[curPage.length - 1].options?.agendaId
				
				const meetingId = uni.getStorageSync('meetingId')
				this.$request({
					url: `/meeting/vote/current/list/${meetingId}`,
					data: {
						agendaId
					}
				}).then(res => {
					this.voteList = res.data || []
				})
			},
			// 投票状态
			mapStatus(status) {
				const mapping = {
					'VOTED': '已投票',
					'NOT-VOTE': '未投票'
				}
				return mapping[status]
			},
			// 跳转投票详情
			navDetail(item) {
				if (item.voteStatus === 'VOTED') {
					return
				}
				uni.navigateTo({
					url: `/pages/voteDetail/voteDetail?id=${item.voteId}`
				})
			}
		}
	}
</script>

<style lang="less">
.vote{
	text-align: center;
	font-weight: 700;
	.vote-content{
		padding: 5vh;
		.vote-title{
			background-color: #540000A6;
			height: 6vh;
			line-height: 6vh;
			font-size: 3vh;
			color: #fff;
		}
		.vote-item{
			background-color: #fff;
			border-bottom: 2rpx solid #ed8c97;
			height: 14vh;
			vertical-align: middle;
			font-size: 3vh;
			.item-data{
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.vote-icon{
				width: 4vh;
				height: 4vh;
			}
		}
	}
}
</style>
