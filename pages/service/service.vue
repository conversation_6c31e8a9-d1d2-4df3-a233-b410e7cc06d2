<template>
	<layout className="app-container service">
		<nav-bar title="会议服务"></nav-bar>
		<view class="service-content">
			<view v-if="!isWaiter" class="service-tips">
				<text class="tips-tag">提示：</text>
				尊敬的领导，本次会议我们将提供以下服务，您可以直接选择服务项，点击下方“发送”即可，我们将在第一时间为您服务，谢谢！
			</view>
			<view v-if="!isWaiter" class="service-main">
				<view class="service-list">
					<view
						v-for="item in serviceItemList"
						:key="item.dictCode"
						class="service-item"
						:class="{active: chosenService.includes(item.dictLabel)}"
						@click="choseService(item)"
					>
						<image class="item-icon" :src="item.dictIcon"></image>
						<text class="item-text">{{item.dictLabel}}</text>
					</view>
				</view>
				<view class="service-send" @click="sendService">发 送</view>
			</view>
			<view class="service-history">
				<view class="history-tips">呼叫记录</view>
				<uni-row class="history-title">
					<uni-col :span="2">序号</uni-col>
					<uni-col :span="14">内容</uni-col>
					<uni-col :span="4">时间</uni-col>
					<uni-col :span="4">状态</uni-col>
				</uni-row>
				<uni-row
					v-for="(item, index) in serviceHistoryList"
					:key="item.serviceId"
					class="history-item"
				>
					<uni-col class="item-data" :span="2">{{index + 1}}</uni-col>
					<uni-col class="item-data item-content" :span="14">{{isWaiter ? item.userName + '：' : ''}}{{item.serviceContent}}</uni-col>
					<uni-col class="item-data" :span="4">{{item.createTime}}</uni-col>
					<uni-col class="item-data" :span="4">
						<view v-if="item.status === 1">已响应</view>
						<view v-else :class="{'can-click': isWaiter}" @click="sendConfirm(item)">{{isWaiter ? '请响应' : '未响应'}}</view>
					</uni-col>
				</uni-row>
			</view>
		</view>
	</layout>
</template>

<script>
	import { mapState } from 'vuex'
	
	const serviceItemList = [{
		id: 0,
		text: '白开水',
		icon: require('@/static/images/icon_water.png')
	}, {
		id: 1,
		text: '麦克风',
		icon: require('@/static/images/icon_micro.png')
	}, {
		id: 2,
		text: '签字笔',
		icon: require('@/static/images/icon_pen.png')
	}, {
		id: 3,
		text: '茶',
		icon: require('@/static/images/icon_tea.png')
	}, {
		id: 4,
		text: '白纸',
		icon: require('@/static/images/icon_paper.png')
	}, {
		id: 5,
		text: '服务员',
		icon: require('@/static/images/icon_user.png')
	}]
	
	export default {
		data() {
			return {
				serviceItemList,
				serviceHistoryList: [],
				// 保存已选择的服务，只存text字段
				chosenService: []
			}
		},
		computed: {
			...mapState('app', [
				'userId',
				'isWaiter',
				'socketTask'
			])
		},
		onLoad() {
			this.queryList()
			this.queryServiceHistoryList()
		},
		methods: {
			// 获取会议服务列表
			queryList() {
				this.$request({
					url: `/system/service/supplies`
				}).then(res => {
					this.serviceItemList = res.data || []
				})
			},
			// 选择会议服务
			choseService(item) {
				if (this.chosenService.includes(item.dictLabel)) {
					this.chosenService = this.chosenService.filter(service => service !== item.dictLabel)
				} else {
					this.chosenService.push(item.dictLabel)
				}
			},
			// 发送会议服务
			sendService() {
				if (!this.chosenService?.length) {
					uni.showToast({
						title: '请选择会议服务',
						icon: 'none'
					})
				} else {
					const msg = `您好，会议秘书，我需要${this.chosenService.join('，')}`
					this.socketTask.send({
						type: 'service',
						meetingId: uni.getStorageSync('meetingId'),
						from: this.userId,
						text: msg
					}).then(() => {
						uni.showToast({
							title: '发送成功',
							icon: 'none'
						})
						this.chosenService = []
						this.queryServiceHistoryList()
					})
				}
			},
			// 查询呼叫记录
			queryServiceHistoryList() {
				const meetingId = uni.getStorageSync('meetingId')
				this.$request({
					url: `/system/service/current/list/${meetingId}`
				}).then(res => {
					this.serviceHistoryList = res.data || []
				})
			},
			// 发送服务确认
			sendConfirm(item) {
				// 只有会议服务人员才能点击
				if (item.status !== 1 && this.isWaiter) {
					this.$request({
						url: `/system/service/confirm/${item.serviceId}`
					}).then(res => {
						this.queryServiceHistoryList()
						uni.showToast({
							title: '响应成功',
							icon: 'none'
						})
					})
				}
			}
		}
	}
</script>

<style lang="less">
.service{
	.service-content{
		flex: 1;
		height: 100%;
		overflow-y: auto;
		padding-bottom: 15vh;
		.service-tips{
			color: #fff;
			font-size: 3.5vh;
			padding: 3vh 5vh;
			border-bottom: 1rpx solid #c9c9c9;
			.tips-tag{
				font-size: 3vh;
			}
		}
		.service-main{
			padding: 8vh;
			display: flex;
			flex-direction: column;
			align-items: center;
			.service-list{
				display: flex;
				align-items: center;
				justify-content: space-around;
				width: 100%;
				.service-item{
					width: 18vh;
					height: 18vh;
					border-radius: 50%;
					background-color: #79a8fa;
					color: #fff;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					&.active{
						background-color: #ff7075;
					}
					.item-icon{
						width: 6vh;
						height: 6vh;
					}
					.item-text{
						margin-top: 1vh;
						font-size: 3.2vh;
					}
				}
			}
			.service-send{
				width: 30vh;
				height: 9vh;
				background: linear-gradient(180deg, #FF6711 0%, #FFCB44 100%);
				border-bottom: 1vh solid #E58A00;
				box-shadow: 0px 1.2vh 6.5vh #98000C;
				border-radius: 2.5vh;
				font-size: 3.5vh;
				color: #fff;
				line-height: 8vh;
				text-align: center;
				margin-top: 10vh;
			}
		}
		.service-history{
			padding: 0 5vh;
			.history-tips{
				color: #fff;
				font-size: 3vh;
			}
			.history-title{
				background-color: #540000A6;
				height: 6vh;
				line-height: 6vh;
				font-size: 3vh;
				color: #fff;
				text-align: center;
				margin-top: 3vh;
			}
			.history-item{
				background-color: #fff;
				border-bottom: 2rpx solid #ed8c97;
				vertical-align: middle;
				font-size: 3vh;
				padding: 2vh 0;
				.item-data{
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					&.item-content{
						justify-content: start;
					}
					.can-click{
						color: #79a8fa;
					}
				}
			}
		}
	}
}
</style>
