<template>
	<view class="app-container score">
		<nav-bar title="议程评分"></nav-bar>
		<view class="score-main">
			<template v-if="isScored">
				<view class="score-info">您的评分是：{{score}}</view>
			</template>
			<template v-else>
				<input
					v-model="score"
					type="number"
					class="score-input"
					focus
					placeholder="请输入你的评分(整数)"
					@confirm="handleSubmit"
				>
				<view class="score-btn" @click="handleSubmit">提交</view>
			</template>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isScored: true,
				score: ''
			};
		},
		onLoad() {
			this.queryScore()
		},
		methods: {
			queryScore() {
				const curPage = getCurrentPages()
				const agendaId = curPage[curPage.length - 1].options?.id
				this.$request({
					url: `/meeting/agenda/score/isScored/${agendaId}`
				}).then(res => {
					this.isScored = res.data.isScored
					this.score = res.data.isScored ? res.data.score : ''
				})
			},
			handleSubmit() {
				if (!this.score?.trim()) {
					return uni.showToast({
						title: '请输入您的评分(整数)',
						icon: 'none'
					})
				}
				if (!/^[123456789]\d*$/.test(this.score?.trim())) {
					return uni.showToast({
						title: '请输入正确的评分(整数)',
						icon: 'none'
					})
				}
				uni.showModal({
					title: '提示',
					content: '确认提交评分吗？',
					success: (modal) => {
						if (modal.confirm) {
							const curPage = getCurrentPages()
							const agendaId = curPage[curPage.length - 1].options?.id
							const data = {
								agendaId,
								score: this.score
							}
							this.$request({
								url: '/meeting/agenda/score',
								method: 'PUT',
								data
							}).then(res => {
								uni.showToast({
									title: '评分成功',
									mask: true,
								})
								this.queryScore()
							})
						}
					}
				})
			}
		},
	}
</script>

<style lang="less" scoped>
.score{
	.score-main{
		padding-top: 20vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.score-info{
			font-size: 5vh;
			color: #fff;
		}
		.score-input{
			width: 40vh;
			height: 8vh;
			background-color: #fff;
			border-radius: 1vh;
			padding: 0 2vh;
			font-size: 3vh;
		}
		.score-btn{
			margin-top: 10vh;
			width: 35vh;
			height: 10vh;
			line-height: 9vh;
			font-weight: 700;
			font-size: 4vh;
			text-align: center;
			background: linear-gradient(180deg, #FF6711 0%, #FFCB44 100%);
			border-bottom: 1vh solid #E58A00;
			box-shadow: 0px 1.2vh 6.5vh #98000C;
			border-radius: 2.5vh;
			color: #fff;
		}
	}
}
</style>
