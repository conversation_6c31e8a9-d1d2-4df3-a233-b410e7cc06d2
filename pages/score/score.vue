<template>
	<layout className="app-container score">
		<nav-bar title="议程评分"></nav-bar>
		<view class="score-main">
			<template v-if="isScored">
				<view class="score-info">
					您的评分如下：
					<view class="info-item">项目得分：{{ form.projectScore }}分</view>
					<!-- <view class="info-item">客户经理得分：{{form.preSaleScore}}分</view>
					<view class="info-item">解决方案经理得分：{{form.solutionScore}}分</view>
					<view class="info-item">交付经理得分：{{form.deliveryScore}}分</view> -->
				</view>
			</template>
			<template v-else>
				<uni-row class="score-row">
					<uni-col
						:span="10"
						class="score-col col-label"
					>
						项目得分：
					</uni-col>
					<uni-col
						:span="14"
						class="score-col col-input"
					>
						<input
							v-model="form.projectScore"
							type="number"
							class="score-input"
							placeholder="最大分值100分"
							@confirm="handleSubmit"
						/>
					</uni-col>
					<!-- <uni-col :span="24" class="score-col col-tips">
						此项打分从以下方面进行评价：项目创新性（20分）、项目预期价值效益（20分）、可复制推广性（20分）、回答现场提问（10分），此处打分合计不超过70分。
					</uni-col> -->
				</uni-row>
				<!-- <uni-row class="score-row">
					<uni-col :span="10" class="score-col col-label">
						客户经理得分：
					</uni-col>
					<uni-col :span="14" class="score-col col-input">
						<input
							v-model="form.preSaleScore"
							type="number"
							class="score-input"
							placeholder="最大分值10分"
							@confirm="handleSubmit"
						>
					</uni-col>
				</uni-row>
				<uni-row class="score-row">
					<uni-col :span="10" class="score-col col-label">
						解决方案经理得分：
					</uni-col>
					<uni-col :span="14" class="score-col col-input">
						<input
							v-model="form.solutionScore"
							type="number"
							class="score-input"
							placeholder="最大分值10分"
							@confirm="handleSubmit"
						>
					</uni-col>
				</uni-row>
				<uni-row class="score-row">
					<uni-col :span="10" class="score-col col-label">
						交付经理得分：
					</uni-col>
					<uni-col :span="14" class="score-col col-input">
						<input
							v-model="form.deliveryScore"
							type="number"
							class="score-input"
							placeholder="最大分值10分"
							@confirm="handleSubmit"
						>
					</uni-col>
				</uni-row> -->
				<view
					class="score-btn"
					@click="handleSubmit"
				>
					提交
				</view>
				<view
					class="score-rules"
					:style="{ backgroundImage: 'url(' + bgImg + ')' }"
				></view>
			</template>
		</view>
	</layout>
</template>

<script>
export default {
	data() {
		return {
			bgImg: '',
			isScored: false,
			form: {
				projectScore: '',
				preSaleScore: '',
				solutionScore: '',
				deliveryScore: ''
			}
		};
	},
	onLoad() {
		this.queryScore();
		this.getBgImg();
	},
	methods: {
		getBgImg() {
			// this.bgImg = 'https://picsum.photos/id/800/400'
			const meetingId = uni.getStorageSync('meetingId');
			this.$request({
				url: `/meeting/agenda/score2/score/rule/${meetingId}`,
				needLogin: false
			}).then((res) => {
				this.bgImg = res.data || '';
			});
		},

		queryScore() {
			const curPage = getCurrentPages();
			const agendaId = curPage[curPage.length - 1].options?.id;
			this.$request({
				url: `/meeting/agenda/score2/isScored/${agendaId}`
			}).then((res) => {
				this.isScored = false;
				if (res.data.isScored) {
					this.form = {
						projectScore: res.data.projectScore,
						preSaleScore: res.data.preSaleScore,
						solutionScore: res.data.solutionScore,
						deliveryScore: res.data.deliveryScore
					};
				}
			});
		},
		handleSubmit() {
			const regNum = /^\d+$/;
			if (!regNum.test(this.form.projectScore)) {
				return uni.showToast({
					title: '请输入正确的项目得分',
					icon: 'none'
				});
			}
			if (this.form.projectScore < 0 || this.form.projectScore > 100) {
				return uni.showToast({
					title: '请输入0-100之间的项目得分',
					icon: 'none'
				});
			}
			// if (!regNum.test(this.form.preSaleScore)) {
			// 	return uni.showToast({
			// 		title: '请输入正确的客户经理得分',
			// 		icon: 'none'
			// 	})
			// }
			// if (this.form.preSaleScore < 0 || this.form.preSaleScore > 10) {
			// 	return uni.showToast({
			// 		title: '请输入0-10之间的客户经理得分',
			// 		icon: 'none'
			// 	})
			// }
			// if (!regNum.test(this.form.solutionScore)) {
			// 	return uni.showToast({
			// 		title: '请输入正确的解决方案经理得分',
			// 		icon: 'none'
			// 	})
			// }
			// if (this.form.solutionScore < 0 || this.form.solutionScore > 10) {
			// 	return uni.showToast({
			// 		title: '请输入0-10之间的解决方案经理得分',
			// 		icon: 'none'
			// 	})
			// }
			// if (!regNum.test(this.form.deliveryScore)) {
			// 	return uni.showToast({
			// 		title: '请输入正确的交付经理得分',
			// 		icon: 'none'
			// 	})
			// }
			// if (this.form.deliveryScore < 0 || this.form.deliveryScore > 10) {
			// 	return uni.showToast({
			// 		title: '请输入0-10之间的交付经理得分',
			// 		icon: 'none'
			// 	})
			// }
			uni.showModal({
				title: '提示',
				content: '确认提交评分吗？',
				success: (modal) => {
					if (modal.confirm) {
						const curPage = getCurrentPages();
						const agendaId = curPage[curPage.length - 1].options?.id;
						const data = {
							agendaId,
							...this.form
						};
						this.$request({
							url: '/meeting/agenda/score2',
							method: 'POST',
							data
						}).then((res) => {
							uni.showToast({
								title: '评分成功',
								mask: true
							});
							this.queryScore();
						});
					}
				}
			});
		}
	}
};
</script>

<style lang="less" scoped>
.score {
	.score-main {
		padding-top: 5vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.score-info {
			font-size: 5vh;
			color: #fff;
			.info-item {
				font-size: 3.5vh;
				&:nth-child(1) {
					margin-top: 4vh;
				}
			}
		}
		.score-row {
			width: 80vh;
			& + .score-row {
				margin-top: 5vh;
			}
			.score-col {
				font-size: 3vh;
				height: 8vh;
				display: flex;
				align-items: center;
				&.col-label {
					color: #fff;
				}
				&.col-tips {
					font-size: 2.5vh;
					color: #d9d9d9;
					line-height: 1.2;
					margin-top: 1vh;
				}
			}
		}
		.score-input {
			width: 40vh;
			height: 8vh;
			background-color: #fff;
			border-radius: 1vh;
			padding: 0 2vh;
			font-size: 3vh;
		}
		.score-btn {
			margin-top: 10vh;
			width: 35vh;
			height: 10vh;
			line-height: 9vh;
			font-weight: 700;
			font-size: 4vh;
			text-align: center;
			background: linear-gradient(180deg, #ff6711 0%, #ffcb44 100%);
			border-bottom: 1vh solid #e58a00;
			box-shadow: 0px 1.2vh 6.5vh #98000c;
			border-radius: 2.5vh;
			color: #fff;
		}
		.score-rules {
			margin-top: 30rpx;
			margin-left: 80rpx;
			width: 800rpx;
			height: 400rpx;
			background-repeat: no-repeat;
			background-size: 90% 60%;
		}
	}
}
</style>
