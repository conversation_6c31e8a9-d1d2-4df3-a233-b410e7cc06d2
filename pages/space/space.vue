<template>
	<view class="app-container space">
		<nav-bar title="会议空间"></nav-bar>
		<view class="space-main">
			<view class="space-side">
				<view
					v-for="item in sideList"
					:key="item.id"
					class="side-item"
					:class="{active: item.name === activeSide}"
					@click="handleClick(item)"
				>{{item.text}}</view>
			</view>
			<view class="space-content">
				<space-sign v-if="activeSide === 'sign'" :list="signList"></space-sign>
				<space-vote v-if="activeSide === 'vote'" :list="voteList"></space-vote>
			</view>
		</view>
	</view>
</template>

<script>
	import SpaceVote from '@/components/SpaceVote/SpaceVote.vue'
	import SpaceSign from '@/components/SpaceSign/SpaceSign.vue'
	
	const sideList = [{
		id: 0,
		name: 'sign',
		text: '签到管理'
	}, {
		id: 1,
		name: 'vote',
		text: '投票管理'
	}]
	export default {
		components: {
			SpaceVote,
			SpaceSign
		},
		data() {
			return {
				sideList,
				activeSide: '',
				showCom: {
					sign: SpaceSign,
					vote: SpaceVote
				},
				voteList: [],
				signList: []
			};
		},
		onLoad() {
			this.handleClick(this.sideList[0])
		},
		methods: {
			handleClick(item) {
				this.activeSide = item.name
				switch(item.name) {
					case 'vote':
						this.queryVoteList()
						break
					case 'sign':
						this.querySignList()
						break
					default:
						console.log('default')
				}
			},
			// 查询投票列表
			queryVoteList() {
				const meetingId = uni.getStorageSync('meetingId')
				this.$request({
					url: `/meeting/vote/list/${meetingId}`
				}).then(res => {
					this.voteList = res.data || []
				})
			},
			// 查询签到列表
			querySignList() {
				const meetingId = uni.getStorageSync('meetingId')
				this.$request({
					url: `/system/sign/current/list/${meetingId}`
				}).then(res => {
					this.signList = res.data || []
				})
			}
		}
	}
</script>

<style lang="less">
.space-main{
	height: 100%;
	width: 100%;
	display: flex;
	.space-side{
		width: 20%;
		height: calc(100% - 9vh);
		overflow-y: auto;
		background-color: #96e9e1;
		.side-item{
			line-height: 9vh;
			font-size: 3vh;
			padding: 0 3vh;
			&.active{
				background-color: #85bde5;
			}
		}
	}
	.space-content{
		width: 80%;
		height: calc(100% - 9vh);
		overflow-y: auto;
		background-color: #fff;
	}
}
</style>
