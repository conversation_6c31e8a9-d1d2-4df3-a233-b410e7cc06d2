<template>
	<layout className="app-container vote-detail">
		<nav-bar title="投票详情"></nav-bar>
		<view class="vote-content">
			<view class="vote-title">{{voteTitle}}</view>
			<view class="vote-options">
				<view
					v-for="(item, index) in voteOptions"
					:key="index"
					class="vote-item"
					:class="{active: chosenVote === item}"
					@click="choseVoteOption(item)"
				>{{item}}</view>
			</view>
			<view class="vote-submit" @click="submitVote">
				提交
			</view>
		</view>
	</layout>
</template>

<script>
	export default {
		data() {
			return {
				voteTitle: '',
				voteOptions: [],
				chosenVote: undefined
			}
		},
		onLoad() {
			this.queryVoteDetail()
		},
		methods: {
			// 获取投票详情
			queryVoteDetail() {
				const curPage = getCurrentPages()
				const voteId = curPage[curPage.length - 1].options?.id
				this.$request({
					url: `/meeting/vote/current/${voteId}`
				}).then(res => {
					this.voteTitle = res.data.topic
					this.voteOptions = res.data?.voteModel?.split(',') || []
				})
			},
			// 选择投票
			choseVoteOption(option) {
				this.chosenVote = option
			},
			// 提交投票
			submitVote() {
				if (!this.chosenVote) {
					uni.showToast({
						title: '请选择投票选项',
						icon: 'none'
					})
					return
				}
				const curPage = getCurrentPages()
				const voteId = curPage[curPage.length - 1].options?.id
				const token = uni.getStorageSync('token')
				const data = {
					voteId,
					voteResult: this.chosenVote
				}
				this.$request({
					url: `/meeting/vote/user`,
					method: 'POST',
					data
				}).then(res => {
					uni.showToast({
						title: '投票成功',
						mask: true,
					})
					setTimeout(uni.navigateBack, 1500)
				})
			}
		}
	}
</script>

<style lang="less">
.vote-detail{
	.vote-content{
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		height: 100%;
		color: #fff;
		padding: 5vh 20vh 20vh;
		.vote-title{
			font-size: 5vh;
			font-weight: 700;
		}
		.vote-options{
			display: flex;
			justify-content: space-between;
			width: 80%;
			.vote-item{
				width: 22vh;
				height: 22vh;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 16px;
				font-size: 4vh;
				font-weight: 700;
				background: linear-gradient(180deg, #5EF8AE 0%, #227FC2 100%);
				position: relative;
				overflow: hidden;
				&.active::after{
					content: "";
					position: absolute;
					right: 0;
					top: 0;
					display: block;
					width: 8vh;
					height: 5vh;
					background: url(~@/static/images/icon_gou.png) center no-repeat;
					background-size: 100%;
				}
				&:nth-child(1) {
					background: linear-gradient(180deg, #5EF8AE 0%, #227FC2 100%);
				}
				&:nth-child(2) {
					background: linear-gradient(180deg, #FF7223 0%, #CB1111 100%);
				}
				&:nth-child(3) {
					background: linear-gradient(180deg, #D1D0D0 0%, #6C6C6C 100%);
				}
			}
		}
		.vote-submit{
			width: 35vh;
			height: 10vh;
			line-height: 9vh;
			font-weight: 700;
			font-size: 4vh;
			text-align: center;
			background: linear-gradient(180deg, #FF6711 0%, #FFCB44 100%);
			border-bottom: 1vh solid #E58A00;
			box-shadow: 0px 1.2vh 6.5vh #98000C;
			border-radius: 2.5vh;
		}
	}
}
</style>
