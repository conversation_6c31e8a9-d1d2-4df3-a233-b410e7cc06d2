<template>
	<layout className="app-container agenda">
		<nav-bar title="会议议程"></nav-bar>
		<view class="agenda-list">
			<agenda-item
				v-for="item in agendaList"
				:key="item.agendaId"
				:data="item"
				@refresh="queryAgendaList"
			></agenda-item>
		</view>
	</layout>
</template>

<script>
	import AgendaItem from '@/components/AgendaItem/AgendaItem.vue'

	export default {
		components: {
			AgendaItem
		},
		data() {
			return {
				agendaList: []
			}
		},
		onLoad() {
			this.$bus.$on('refresh-agenda', this.queryAgendaList)
		},
		onShow() {
			this.queryAgendaList()
		},
		methods: {
			// 获取议程列表
			queryAgendaList() {
				const meetingId = uni.getStorageSync('meetingId')
				this.$request({
					url: `/meeting/agenda/current/${meetingId}`
				}).then(res => {
					this.agendaList = res.data || []
				})
			},
		}
	}
</script>

<style lang="less">
.agenda{
	display: flex;
	flex-direction: column;
	align-items: center;
	.agenda-list{
		width: 100%;
		height: 100%;
		flex: 1;
		overflow-y: auto;
		color: #fff;
	}
}
</style>
