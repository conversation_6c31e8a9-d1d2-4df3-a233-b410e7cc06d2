<template>
	<layout className="app-container login">
		<log-out></log-out>
		<view class="meeting-title" :style="{color: fontColor}">{{title}}</view>
		<view class="meeting-user">
			<view class="user-text" :style="{color: fontColor}">参会人员</view>
			<view class="user-name" @click="showUserList">{{ userName || '请选择' }}</view>
		</view>
		<view class="meeting-time">
			<view class="time-text">开始时间：</view>
			<view class="time-value">{{beginTime}}</view>
		</view>
		<button class="meeting-btn" @click="enterMeeting">进入会议</button>
		<uni-popup ref="userPopup" type="dialog">
			<view class="dialog-body">
				<uni-row :gutter="10">
					<uni-col :span="16">
						<input class="user-input" type="text" v-model="searchName" placeholder="请输入名字搜索">
					</uni-col>
					<uni-col :span="8">
						<button @click="handleSearch">搜索</button>
					</uni-col>
				</uni-row>
				<uni-row class="user-list">
					<view
						v-for="item in userListSearch"
						:key="item.userId"
						class="user-item"
						@click="choseUser(item)"
					>{{item.userName}}</view>
				</uni-row>
				<!-- <uni-row>
					<button class="user-btn">确定</button>
				</uni-row> -->
			</view>
		</uni-popup>
	</layout>
</template>

<script>
	import { parseTime } from '@/utils/common.js'
	import { apiUrl, socketUrl } from '@/utils/config.js'
	import wsRequest from '@/utils/socket.js'
	import { mapState } from 'vuex'
	
	export default {
		data() {
			return {
				backImg: uni.getStorageSync('backImg'),
				title: '欢迎龙泉村村委曾书记一行莅临',
				beginTime: '01月03日 14:00',
				userId: undefined,
				userName: '',
				searchName: '',
				userList: [],
				userListSearch: []
			}
		},
		computed: {
			...mapState('app', [
				'socketTask',
				'fontColor'
			])
		},
		onLoad() {
			// uni.clearStorage()
			const roomId = uni.getStorageSync('roomId')
			if (!roomId) {
				return uni.redirectTo({
					url: '/pages/index/index'
				})
			}
			this.queryMeetingInfo()
		},
		methods: {
			// 获取会议信息
			queryMeetingInfo() {
				const roomId = uni.getStorageSync('roomId')
				uni.request({
					url: `${apiUrl}/meeting/current/${roomId}`,
					success: res => {
						const { data } = res
						console.log(data)
						if (data.code === 200) {
							this.title = data.data.title
							this.beginTime = parseTime(data.data.beginTime, '{m}月{d}日 {h}:{i}')
						}
					}
				})
			},
			// 获取用户列表
			async queryUserList() {
				const roomId = uni.getStorageSync('roomId')
				const res = await this.$request({
					url: `/meeting/room/current/${roomId}`,
					needLogin: false
				})
				this.userList = res.data || []
				this.userListSearch = this.userList
			},
			// 进入会议
			enterMeeting() {
				if (!this.userId) {
					uni.showToast({
						title: '请选择参会人员',
						icon: 'none'
					})
					return
				}
				this.$request({
					url: `/paid/login/${this.userId}`,
					needLogin: false
				}).then(res => {
					console.log('login', res)
					uni.setStorageSync('token', res.token)
					this.$store.commit('app/SET_USER_ID', this.userId)
					this.$store.commit('app/SET_USERNAME', this.userName)
					this.$store.commit('app/TOGGLE_SCREEN', !!res.isScreen)
					this.$store.commit('app/SET_HOST', !!res.host)
					this.$store.commit('app/SET_WAITER', !!res.waiter)
					const socketTask = new wsRequest(`${socketUrl}/webSocket/`, this.userId)
					this.$store.commit('app/SET_SOCKET_TASK', socketTask)
					uni.navigateTo({
						url: '/pages/home/<USER>'
					})
				})
			},
			// 打开人员选择弹窗
			async showUserList() {
				this.searchName = ''
				await this.queryUserList()
				this.$refs.userPopup.open()
			},
			// 选择参会人员
			choseUser(user) {
				this.userId = user.userId
				this.userName = user.userName
				this.$refs.userPopup.close()
			},
			// 点击搜索按钮
			handleSearch() {
				const userList = this.userList
				const userListSearch = userList.filter(item => item.userName.includes(this.searchName?.trim()))
				this.userListSearch = userListSearch
			},
			connectSocket() {
				uni.connectSocket({
					url: `${socketUrl}/webSocket/${this.userId}`,
					header: {
						'content-type': 'application/json'
					},
					method: 'GET'
				})
				uni.onSocketOpen(res => {
					console.log('webSocket 已打开')
				})
				uni.onSocketError(function (res) {
					console.log('WebSocket连接打开失败，请检查！');
				});
				uni.onSocketClose(function() {
					console.log('Websocket连接已关闭')
				})
				uni.onSocketMessage(res => {
					console.log('收到服务器内容app.vue', res.data)
					const resObj = JSON.parse(res.data)
					// 刷新议程
					if (resObj.type === 'refreshAgenda') {
						this.$bus.$emit('refresh-agenda')
					}
					// 结束同屏
					// if (resObj.type === 'screenEnd') {
					// 	this.$store.commit('app/TOGGLE_SHARING', false)
					// 	this.$store.commit('app/SET_VIDEO_SRC', '')
					// }
				})
			}
		}
	}
</script>

<style lang="less">
.login{
	display: flex;
	flex-direction: column;
	align-items: center;
	.meeting-title{
		font-weight: 700;
		text-align: center;
		font-size: 6vh;
		line-height: 1.5;
		color: #FFC267;
		padding: 7.5vh 0;
	}
	.meeting-user{
		display: flex;
		flex-direction: column;
		align-items: center;
		.user-text{
			font-size: 5vh;
			color: #FFC267;
			font-weight: 700;
		}
		.user-name{
			width: 60vh;
			text-align: center;
			font-size: 12vh;
			border-bottom: 2rpx solid #FC8696;
			color: #fff;
			font-family: hyxk;
		}
	}
	.meeting-time{
		width: 60%;
		margin-top: 6vh;
		background: linear-gradient(180deg, rgba(82, 0, 1, 0.65) 0%, rgba(82, 0, 1, 0) 116.16%);
		border-radius: 2vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 1.5vh;
		.time-text{
			color: #FC8696;
			font-weight: 500;
			font-size: 3vh;
		}
		.time-value{
			color: #fff;
			font-weight: 700;
			font-size: 4vh;
		}
	}
	.meeting-btn{
		width: 40vh;
		height: 13vh;
		background: linear-gradient(180deg, #FF6711 0%, #FFCB44 100%);
		border-bottom: 1vh solid #E58A00;
		box-shadow: 0px 1.2vh 6.5vh #98000C;
		border-radius: 2.5vh;
		font-size: 5vh;
		color: #fff;
		font-weight: 700;
		line-height: 12vh;
		position: fixed;
		bottom: 5vh;
	}
	.dialog-body{
		width: 60vh;
		background-color: #fff;
		border-radius: 3.5vh;
		padding: 5vh;
		max-height: 70vh;
		overflow-y: auto;
		.user-input{
			background-color: #e0e0e0;
			padding: 9rpx 8rpx;
			border-radius: 3rpx;
		}
		.user-list{
			display: flex;
			flex-wrap: wrap;
			margin-top: 4vh;
			.user-item{
				width: 13vh;
				padding: 1vh 0;
				text-align: center;
				border: 2rpx solid #00f;
				border-radius: 0.5vh;
				margin-right: 3vh;
				margin-bottom: 3vh;
			}
		}
		.user-btn{
			margin-top: 3.5vh;
		}
	}
}
</style>
