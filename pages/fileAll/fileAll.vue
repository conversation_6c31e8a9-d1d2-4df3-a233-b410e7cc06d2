<template>
	<layout className="app-container file-all">
		<nav-bar title="会议资料"></nav-bar>
		<view class="file-content">
			<view class="file-agenda"
				v-for="(item, index) in fileList"
				:key="index"
			>
				<view class="agenda-title" @click="toggleList(item)">
					{{item.agendaName}}
					<uni-icons class="title-icon" :type="item.show ? 'top' : 'bottom'"></uni-icons>
				</view>
				<view class="file-list" :class="{show: item.show}">
					<file-item
						v-for="file in item.files"
						:key="file.annexesId"
						:file="file"
						class="list-item"
					>
						{{file.fileName}}
					</file-item>
				</view>
			</view>
		</view>
	</layout>
</template>

<script>
	import FileItem from '@/components/FileItem/FileItem.vue'

	export default {
		components: {
			FileItem
		},
		data() {
			return {
				fileList: []
			}
		},
		onLoad() {
			this.queryFileList()
		},
		methods: {
			// 获取文件列表
			queryFileList() {
				const meetingId = uni.getStorageSync('meetingId')
				this.$request({
					url: `/meeting/agendas/${meetingId}`
				}).then(res => {
					this.fileList = res.data || []
				})
			},
			// 切换列表显示 
			toggleList(item) {
				this.$set(item, 'show', !item.show)
			}
		}
	}
</script>

<style lang="less">
.file-all{
	.file-content{
		height: calc(100% - 9vh);
		overflow-y: auto;
		.file-agenda{
			border-bottom: 1rpx dotted #cecece;
			.agenda-title{
				color: #fff;
				font-size: 3.5vh;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: rgba(82, 0, 1, 0.65);
				padding: 2vh 5vh;
				.title-icon{
					color: #fff !important;
					font-size: 4vh !important;
				}
			}
			.file-list{
				display: none;
				&.show{
					display: block;
				}
				.list-item{
					
				}
			}
		}
	}
}
</style>
