<template>
	<layout className="app-container file">
		<nav-bar title="会议资料"></nav-bar>
		<view class="file-list">
			<file-item
				v-for="item in fileList"
				:key="item.annexesId"
				:file="item"
			></file-item>
		</view>
	</layout>
</template>

<script>
	import FileItem from '@/components/FileItem/FileItem.vue'
	
	export default {
		components: {
			FileItem
		},
		data() {
			return {
				fileList: []
			}
		},
		onLoad() {
			this.queryAgendaFiles()
		},
		methods: {
			// 获取议程资料
			queryAgendaFiles() {
				const curPage = getCurrentPages()
				const agendaId = curPage[curPage.length - 1].options?.id
				if (agendaId) {
					// 查询议程文件
					this.$request({
						url: `/meeting/annexes/files/${agendaId}`
					}).then(res => {
						this.fileList = res.data || []
					})
				}
			},
		}
	}
</script>

<style lang="less">
.file-list{
	height: 100%;
	flex: 1;
	overflow-y: auto;
	padding-top: 3vh;
}
</style>
