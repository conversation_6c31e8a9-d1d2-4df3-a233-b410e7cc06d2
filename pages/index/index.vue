<template>
	<layout className="app-container index">
		<uni-popup ref="popup" type="dialog" :maskClick="false">
			<view class="dialog-body">
				<uni-row class="dialog-row">
					<uni-col :span="4">
						会议室：
					</uni-col>
					<uni-col :span="20">
						<uni-data-select
							v-model="currentRoom"
							:localdata="roomListArr"
						></uni-data-select>
					</uni-col>
				</uni-row>
				<uni-row class="btn-group">
					<button class="btn" @click="queryRoomList">刷新</button>
					<button class="btn save" @click="choseRoom">保存</button>
				</uni-row>
			</view>
		</uni-popup>
	</layout>
</template>

<script>
	export default {
		data() {
			return {
				roomList: [],
				currentRoom: undefined
			}
		},
		computed: {
			roomListArr() {
				return this.roomList.map(room => ({
						text: room.roomName,
						value: room.roomId
					})
				) || []
			}
		},
		onReady() {
			const roomId = uni.getStorageSync('roomId')
			if (false) {
				uni.redirectTo({
					url: '/pages/login/login'
				})
			} else {
				this.$refs.popup.open()
				this.queryRoomList()
			}
		},
		methods: {
			// 获取会议室
			queryRoomList() {
				this.$request({
					url: `/meeting/room/current/list`,
					needLogin: false
				}).then(res => {
					this.roomList = res.data || []
				})
			},
			// 选择会议室
			choseRoom() {
				const roomId = this.currentRoom
				if (!roomId) {
					uni.showToast({
						title: '请选择会议室',
						icon: 'none'
					})
					return
				}
				const room = this.roomList.find(item => item.roomId === roomId)
				this.$store.commit('app/SET_BACK_IMG', room?.meetingScreenPic)
				this.$store.commit('app/SET_FONT_COLOR', room?.fontColor)
				uni.setStorageSync('roomId', JSON.stringify(roomId))
				uni.redirectTo({
					url: '/pages/login/login'
				})
			}
		}
	}
</script>

<style lang="less">
	.index{
		background-color: #416fff;
		.dialog-body{
			width: 80vh;
			background-color: #fff;
			border-radius: 2vh;
			padding: 8vh;
			.dialog-row{
				display: flex;
				align-items: center;
			}
			.btn-group{
				margin-top: 8vh;
				display: flex;
				.btn{
					width: 22vh;
					&.save{
						background-color: #79a8fa;
						border-color: #79a8fa;
						color: #fff;
					}
				}
			}
		}
	}
</style>
