<template>
	<layout className="app-container home">
		<movable-view
			v-if="isScreen"
			class="share-area"
			direction="all"
			x="1200rpx"
			y="150rpx"
		>
			<view v-if="isSharing" class="share-button" @click="stopScreen">关闭<br>同屏</view>
			<view v-else class="share-button" @click="startScreen">开启<br>同屏</view>
		</movable-view>
		<log-out :show-back="false"></log-out>
		<view class="meeting-title" :style="{color: fontColor}">{{title}}</view>
		<view class="meeting-user">
			<view class="user-text" :style="{color: fontColor}">参会人员</view>
			<view class="user-name">{{userName || '周斌'}}</view>
		</view>
		<view class="meeting-content">
			<view class="meeting-time">
				<view class="time-text">记录：</view>
				<view class="time-value">{{hostName}}</view>
			</view>
			<view class="meeting-time">
				<view class="time-text">会议时间：</view>
				<view class="time-value">{{beginTime}} 至 {{endTime}}</view>
			</view>
			<view v-if="isHost" class="meeting-space" @click="navSpace">
				会议空间
			</view>
		</view>
		<view class="meeting-ctrls">
			<view v-for="item in funcList" :key="item.id" class="ctrl-item" @click="navTo(item)">
				<view v-if="item.id === 4 && serviceTag" class="ctrl-tag"></view>
				<image class="ctrl-icon" :src="item.icon">
				<text class="ctrl-text">{{item.text}}</text>
			</view>
		</view>
	</layout>
</template>

<script>
	import {
		parseTime
	} from '@/utils/common.js'
	import {
		mapState
	} from 'vuex'
	import { srsUrl } from '@/utils/config.js'
	
	const getModel = uni.requireNativePlugin("yuange-YGRSPushModule");
	const keepAlive = uni.requireNativePlugin('Ba-KeepAliveSuit')

	const funcList = [{
		id: 0,
		text: '会议议程',
		icon: '/static/images/icon1.png',
		path: '/pages/agenda/agenda'
	}, {
		id: 1,
		text: '会议资料',
		icon: '/static/images/icon2.png',
		path: '/pages/fileAll/fileAll'
	}, {
		id: 2,
		text: '投票表决',
		icon: '/static/images/icon3.png',
		path: '/pages/vote/vote'
	}, {
		id: 4,
		text: '会议服务',
		icon: '/static/images/icon5.png',
		path: '/pages/service/service'
	}]
	export default {
		data() {
			return {
				title: '欢迎龙泉村村委曾书记一行莅临',
				hostName: '周玉霞',
				beginTime: '14:00',
				endTime: '16:00',
				funcList
			}
		},
		computed: {
			...mapState('app', [
				'isScreen',
				'isHost',
				'isWaiter',
				'userName',
				'serviceTag',
				'userId',
				'socketTask',
				'isSharing',
				'fontColor'
			])
		},
		onLoad() {
			this.queryMeetingInfo()
		},
		methods: {
			// 获取会议信息
			queryMeetingInfo() {
				const roomId = uni.getStorageSync('roomId')
				this.$request({
					url: `/meeting/current/${roomId}`,
					needLogin: false
				}).then(res => {
					this.title = res.data.title
					this.hostName = res.data.hostName
					this.beginTime = parseTime(res.data.beginTime, '{h}:{i}')
					this.endTime = parseTime(res.data.endTime, '{h}:{i}')
					// 缓存meetingId
					uni.setStorageSync('meetingId', JSON.stringify(res.data.meetingId))
				})
			},
			// 跳转会议空间
			navSpace() {
				uni.navigateTo({
					url: '/pages/space/space'
				})
			},
			// 操作跳转
			navTo(item) {
				if (!item.path) {
					uni.showToast({
						title: '敬请期待',
						icon: 'none'
					})
					return
				}
				// 如果角色是会服人员，只能点击会议服务
				if (this.isWaiter && !this.isHost && [0, 1, 2, 3].includes(item.id)) {
					uni.showToast({
						title: '会议服务人员不允许点击',
						icon: 'none'
					})
					return
				}
				// 如果是会议服务，则去掉标记
				if (item.id === 4) {
					this.$store.commit('app/SET_SERVICE_TAG', false)
				}
				uni.navigateTo({
					url: item.path
				})
			},
			// 开启同屏
			startScreen() {
				console.log(`${srsUrl}/live/${this.userId}`)
				getModel.callNativeStartRecordScreenPush({
					'liveUrl': `${srsUrl}/live/${this.userId}`,//推流地址
					'liveFPS': 20, //帧率 设置范围8到30  建议20
					'resolution': 1080 ,//分辨率 枚举值：180、240、360、480、540、720、1080
				},
				(ret) => {
					const meetingId = uni.getStorageSync('meetingId')
					this.socketTask.send({
						type: 'screenStart',
						url: this.userId,
						meetingId
					})
					this.$store.commit('app/TOGGLE_SHARING', true)
					this.$store.commit('app/TOGGLE_ME_SHARING', true)
					console.log('同屏数据', JSON.stringify(ret))
					// 开启保活
					keepAlive.onKeep({
						title: "智能会议",
						content: "智能会议后台运行中",
					},
					(res) => {
						console.log(res);
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 3000
						})
					});
				},
				err => {
					console.log(err)
				})
			},
			stopScreen() {
				uni.showModal({
					title: '提示',
					content: '确认关闭同屏吗？',
					success: (res) => {
						if (res.confirm) {
							getModel.callNativeStopRecordScreenPush({},
								(ret) => {
									const meetingId = uni.getStorageSync('meetingId')
									this.socketTask.send({
										type: 'screenEnd',
										meetingId
									})
									this.$store.commit('app/TOGGLE_SHARING', false)
									this.$store.commit('app/TOGGLE_ME_SHARING', false)
									console.log(JSON.stringify(ret))
								}
							)
						}
					}
				})
			}
		}
	}
</script>

<style lang="less">
	.home {
		display: flex;
		flex-direction: column;
		align-items: center;
		.share-area{
			width: 50rpx;
			height: 50rpx;
			border-radius: 50%;
			background-color: #0082ff;
			.share-button{
				background-color: transparent;
				border: none;
				outline: none;
				color: #fff;
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 16rpx;
				line-height: 1.2;
			}
		}

		.meeting-title {
			font-weight: 700;
			text-align: center;
			font-size: 6vh;
			line-height: 1.5;
			color: #FFC267;
			padding: 7.5vh 0 6.5vh;
		}

		.meeting-user {
			display: flex;
			flex-direction: column;
			align-items: center;

			.user-text {
				font-size: 5vh;
				color: #FFC267;
				font-weight: 700;
			}

			.user-name {
				width: 60vh;
				text-align: center;
				font-size: 12vh;
				color: #fff;
				font-family: hyxk;
			}
		}

		.meeting-content {
			width: 80%;
			margin-top: 6vh;
			background: linear-gradient(180deg, rgba(82, 0, 1, 0.65) 0%, rgba(82, 0, 1, 0) 116.16%);
			border-radius: 2vh;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 1.5vh;
			.meeting-time {
				display: flex;
				flex-direction: column;
				&+.meeting-time {
					padding-left: 8vh;
				}
				.time-text {
					color: #FC8696;
					font-weight: 500;
					font-size: 3vh;
				}
				.time-value {
					color: #fff;
					font-weight: 700;
					font-size: 4vh;
				}
			}
			.meeting-space {
				color: #fff;
				font-size: 4vh;
				border-left: 1rpx solid #fff;
				padding-left: 8vh;
				margin-left: 8vh;
			}
		}

		.meeting-ctrls {
			width: 80%;
			display: flex;
			justify-content: space-between;
			margin-top: 5vh;
			.ctrl-item {
				width: 15vh;
				height: 15vh;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background: linear-gradient(180deg, #FF6711 0%, #FFCB44 100%);
				border-bottom: 1vh solid #E58A00;
				box-shadow: 0px 1.2vh 6.5vh #98000C;
				border-radius: 2.5vh;
				position: relative;

				&:nth-of-type(2),
				&:nth-of-type(3) {
					margin-top: 4vh;
				}

				// &:nth-of-type(3) {
				// 	margin-top: 4vh;
				// }
				.ctrl-tag{
					position: absolute;
					width: 2.5vh;
					height: 2.5vh;
					border-radius: 50%;
					top: -0.5vh;
					left: -0.5vh;
					background-color: #00a3f1;
				}
				.ctrl-icon {
					width: 6vh;
					height: 6vh;
				}
				.ctrl-text {
					font-size: 3vh;
					font-weight: 700;
					color: #fff;
					margin-top: 0.5vh;
				}
			}
		}
	}
</style>
