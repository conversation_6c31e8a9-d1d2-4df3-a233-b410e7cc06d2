<template>
	<!-- <video 
		class="screen-video"
		id="myVideo"
		objectFit="cover"
		is-live
		:controls="false"
		autoplay
		:src="videoSrc"
	></video> -->
	<web-view :src="`${guestUrl}/video?url=${videoSrc}`"></web-view>
</template>

<script>
	import { mapState } from 'vuex'
	import { guestUrl } from '@/utils/config.js'
	
	export default {
		name:"video-player",
		data() {
			return {
				guestUrl
			};
		},
		computed: {
			...mapState('app', [
				'videoSrc',
				'isSharing'
			])
		},
		watch: {
			isSharing(val) {
				console.log('isSharing', val)
				if (!val) {
					uni.navigateBack()
				}
			}
		},
		mounted() {
			// setTimeout(() => {
			// 	const videoContext = uni.createVideoContext('myVideo', this)
			// 	videoContext.play()
			// }, 1000)
		}
	}
</script>

<style lang="scss">
.screen-video{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100vw;
	height: 100vh;
	z-index: 999;
}
</style>